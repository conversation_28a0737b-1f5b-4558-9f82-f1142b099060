window.cookiebanner = new window.CookieBanner({ ignoreAllow: !0, showPoweredBy: !1, theme: cncb_plugin_object.theme, type: cncb_plugin_object.type, blockType: cncb_plugin_object.bannerBlockType, blockPosition: cncb_plugin_object.position, buttonType: cncb_plugin_object.buttonType, corner: cncb_plugin_object.corner, message: cncb_plugin_object.message, link: cncb_plugin_object.link, buttonAllow: cncb_plugin_object.buttonAllow, buttonDismiss: cncb_plugin_object.buttonDismiss, buttonDecline: cncb_plugin_object.buttonDecline, blind: cncb_plugin_object.blind, buttonDirection: cncb_plugin_object.buttonDirection, animation: { type: cncb_plugin_object.animationType, duration: cncb_plugin_object.animationDuration, delay: cncb_plugin_object.animationDelay }, popup: { styles: cncb_plugin_object.popupStyles }, accept: cncb_plugin_object.accept }), wp.customize("cncb_widget_link_show", function (n) { n.bind(function (n) { cookiebanner.update({ link: { styles: { display: n ? "inline" : "none" } } }) }) }), wp.customize("cncb_widget_type", function (n) { n.bind(function (n) { cookiebanner.update({ blockType: n }) }) }), wp.customize("cncb_widget_type", function (n) { n.bind(function (n) { "line" !== n && wp.customize("cncb_vertical_btn").get() && cookiebanner.update({ buttonDirection: "column" }) }) }), wp.customize("cncb_blind_screen", function (n) { n.bind(function (n) { cookiebanner.update({ blind: { visible: n } }) }) }), wp.customize("cncb_position", function (n) { n.bind(function (n) { cookiebanner.update({ blockPosition: n }) }) }), wp.customize("cncb_animation", function (n) { n.bind(function (n) { cookiebanner.update({ animation: { type: n } }) }) }), wp.customize("cncb_type", function (n) { n.bind(function (n) { cookiebanner.update({ type: n }) }) }), wp.customize("cncb_buttons_type", function (n) { n.bind(function (n) { cookiebanner.update({ buttonType: n }) }) }), wp.customize("cncb_vertical_btn", function (n) { n.bind(function (n) { n = n ? "column" : "row", cookiebanner.update({ buttonDirection: n }) }) }), wp.customize("cncb_text", function (n) { n.bind(function (n) { cookiebanner.update({ message: { html: n } }) }) }), wp.customize("cncb_text_font_family", function (n) { n.bind(function (n) { cookiebanner.update({ message: { styles: { "font-family": n } } }) }) }), wp.customize("cncb_text_color", function (n) { n.bind(function (n) { cookiebanner.update({ message: { styles: { color: n } } }) }) }), wp.customize("cncb_link_color", function (n) { n.bind(function (n) { cookiebanner.update({ link: { styles: { color: n } } }) }) }), wp.customize("cncb_link_hover_color", function (n) { n.bind(function (n) { cookiebanner.update({ link: { stylesHover: { color: n } } }) }) }), wp.customize("cncb_allow_text", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { html: n } }) }) }), wp.customize("cncb_dismiss_text", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDismiss: { html: n } }) }) }), wp.customize("cncb_ab_bg_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { "background-color": n } } }), cookiebanner.update({ buttonDismiss: { styles: { "background-color": n } } }) }) }), wp.customize("cncb_ab_gradient", function (n) { n.bind(function (n) { var t = ""; n && (t = wp.customize("cncb_ab_gradient_style").get()), cookiebanner.update({ buttonAllow: { styles: { background: t } } }), cookiebanner.update({ buttonDismiss: { styles: { background: t } } }) }) }), wp.customize("cncb_ab_gradient_style", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { background: n } } }), cookiebanner.update({ buttonDismiss: { styles: { background: n } } }) }) }), wp.customize("cncb_ab_shadow", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_ab_shadow_style").get()), cookiebanner.update({ buttonAllow: { styles: { "box-shadow": t } } }) }) }), wp.customize("cncb_ab_shadow_style", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_ab_shadow_style").get()), cookiebanner.update({ buttonAllow: { styles: { "box-shadow": t } } }) }) }), wp.customize("cncb_ab_border_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { "border-color": n } } }), cookiebanner.update({ buttonDismiss: { styles: { "border-color": n } } }) }) }), wp.customize("cncb_ab_border_width", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { "border-width": n + "px" } } }), cookiebanner.update({ buttonDismiss: { styles: { "border-width": n + "px" } } }) }) }), wp.customize("cncb_ab_border_radius", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { "border-radius": n + "px" } } }), cookiebanner.update({ buttonDismiss: { styles: { "border-radius": n + "px" } } }) }) }), wp.customize("cncb_ab_font_family", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { "font-family": n } } }), cookiebanner.update({ buttonDismiss: { styles: { "font-family": n } } }) }) }), wp.customize("cncb_ab_text_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { styles: { color: n } } }), cookiebanner.update({ buttonDismiss: { styles: { color: n } } }) }) }), wp.customize("cncb_ab_hover_text_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { stylesHover: { color: n } } }), cookiebanner.update({ buttonDismiss: { stylesHover: { color: n } } }) }) }), wp.customize("cncb_ab_hover_bg_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { stylesHover: { background: n } } }), cookiebanner.update({ buttonDismiss: { stylesHover: { background: n } } }) }) }), wp.customize("cncb_ab_hover_border_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { stylesHover: { "border-color": n } } }), cookiebanner.update({ buttonDismiss: { stylesHover: { "border-color": n } } }) }) }), wp.customize("cncb_ab_hover_gradient", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_ab_hover_gradient_style").get()), cookiebanner.update({ buttonAllow: { stylesHover: { background: t } } }), cookiebanner.update({ buttonDismiss: { stylesHover: { background: t } } }) }) }), wp.customize("cncb_ab_hover_gradient_style", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { stylesHover: { background: n } } }), cookiebanner.update({ buttonDismiss: { stylesHover: { background: n } } }) }) }), wp.customize("cncb_decline_text", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { html: n } }) }) }), wp.customize("cncb_db_bg_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { "background-color": n } } }) }) }), wp.customize("cncb_db_gradient", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_db_gradient_style").get()), cookiebanner.update({ buttonDecline: { styles: { background: t } } }) }) }), wp.customize("cncb_db_shadow", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_db_shadow_style").get()), cookiebanner.update({ buttonDecline: { styles: { "box-shadow": t } } }) }) }), wp.customize("cncb_db_shadow_style", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_db_shadow_style").get()), cookiebanner.update({ buttonDecline: { styles: { "box-shadow": t } } }) }) }), wp.customize("cncb_db_border_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { "border-color": n } } }) }) }), wp.customize("cncb_db_border_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { "border-color": n } } }) }) }), wp.customize("cncb_db_border_width", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { "border-width": n + "px" } } }) }) }), wp.customize("cncb_db_border_radius", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { "border-radius": n + "px" } } }) }) }), wp.customize("cncb_db_font_family", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { "font-family": n } } }) }) }), wp.customize("cncb_db_text_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { styles: { color: n } } }) }) }), wp.customize("cncb_db_hover_text_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { stylesHover: { color: n } } }) }) }), wp.customize("cncb_db_hover_bg_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { stylesHover: { background: n } } }) }) }), wp.customize("cncb_db_hover_border_color", function (n) { n.bind(function (n) { cookiebanner.update({ buttonDecline: { stylesHover: { "border-color": n } } }) }) }), wp.customize("cncb_db_hover_gradient", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_db_hover_gradient_style").get()), cookiebanner.update({ buttonDecline: { stylesHover: { background: t } } }) }) }), wp.customize("cncb_db_hover_gradient_style", function (n) { n.bind(function (n) { cookiebanner.update({ buttonAllow: { stylesHover: { background: n } } }) }) }), wp.customize("cncb_widget_link_text", function (n) { n.bind(function (n) { cookiebanner.update({ link: { html: n } }) }) }), wp.customize("cncb_widget_link_href", function (n) { n.bind(function (n) { cookiebanner.update({ link: { href: n } }) }) }), wp.customize("cncb_theme", function (n) { n.bind(function (n) { cookiebanner.update({ theme: n }) }) }), wp.customize("cncb_banner_width", function (n) { n.bind(function (n) { cookiebanner.update({ popup: { styles: { width: n + "px" } } }) }) }), wp.customize("cncb_banner_margin", function (n) { n.bind(function (n) { var t = n.split(" "); cookiebanner.update({ popup: { styles: { "margin-top": t[0], "margin-right": t[1], "margin-bottom": t[2], "margin-left": t[3] } } }) }) }), wp.customize("cncb_border_width", function (n) { n.bind(function (n) { cookiebanner.update({ popup: { styles: { "border-width": n + "px" } } }) }) }), wp.customize("cncb_border_radius", function (n) { n.bind(function (n) { cookiebanner.update({ popup: { styles: { "border-radius": n + "px" } } }) }) }), wp.customize("cncb_border_color", function (n) { n.bind(function (n) { cookiebanner.update({ popup: { styles: { "border-color": n } } }) }) }), wp.customize("cncb_show_border", function (n) { n.bind(function (n) { var t = "none"; n && (t = "solid"), cookiebanner.update({ popup: { styles: { "border-style": t } } }) }) }), wp.customize("cncb_shadow", function (n) { n.bind(function (n) { var t = "none"; n && (t = wp.customize("cncb_shadow_style").get()), cookiebanner.update({ popup: { styles: { "box-shadow": t } } }) }) }), wp.customize("cncb_shadow_style", function (n) { n.bind(function (n) { cookiebanner.update({ popup: { styles: { "box-shadow": n } } }) }) }), wp.customize("cncb_animation_delay", function (n) { n.bind(function (n) { cookiebanner.update({ animation: { delay: n + "ms" } }) }) }), wp.customize("cncb_animation_duration", function (n) { n.bind(function (n) { cookiebanner.update({ animation: { duration: n + "ms" } }) }) });