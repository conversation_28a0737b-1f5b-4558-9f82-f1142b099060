jQuery(document).ready(function (n) { "use strict"; new function n(e) { if (!e.container) { console.error("Container is not exist."); return } var t = this; if (t.container = e.container, t.inputValue = t.container.querySelector(".js-value"), t.inputValueLeft = t.container.querySelector(".js-left"), t.inputValueRight = t.container.querySelector(".js-right"), t.inputValueBottom = t.container.querySelector(".js-bottom"), t.inputValueTop = t.container.querySelector(".js-top"), !t.inputValue || !t.inputValueLeft || !t.inputValueRight || !t.inputValueBottom || !t.inputValueTop) { console.error("All inputs needed."); return } function i() { t.inputValue.value = o(t.inputValueTop.value) + " " + o(t.inputValueRight.value) + " " + o(t.inputValueBottom.value) + " " + o(t.inputValueLeft.value), jQuery(t.inputValue).trigger("input") } function o(n) { return "" === n ? "auto" : (n || 0) + "px" } (function n() { var e = t.inputValue.value; if (!e) { t.inputValueTop.value = "", t.inputValueRight.value = "", t.inputValueBottom.value = "", t.inputValueLeft.value = ""; return } var i = e.split(" "); t.inputValueTop.value = parseInt(i[0]), t.inputValueRight.value = parseInt(i[1]), t.inputValueBottom.value = parseInt(i[2]), t.inputValueLeft.value = parseInt(i[3]) })(), t.inputValueLeft.addEventListener("input", i, !1), t.inputValueRight.addEventListener("input", i, !1), t.inputValueBottom.addEventListener("input", i, !1), t.inputValueTop.addEventListener("input", i, !1) }({ container: document.querySelector(".js-margin-group") }) }), function (n, e) { jQuery(document).on("click", "#cncb_step_2", function (n) { var e, t; n.preventDefault(), e = document.querySelector("#sub-accordion-section-cncb_wizard_section .customize-section-back"), t = new MouseEvent("click", { bubbles: !0, cancelable: !0, view: window }), e.dispatchEvent(t) }), e.bind("ready", function () { var t = this, i = ["top", "bottom"], o = ["top-left", "top-right", "bottom-left", "bottom-right", "center"]; function c(t, i, o) { if (n.each(t, function (n, t) { e.control("cncb_position").container.find('input[value="' + t + '"]').prop("disabled", !1) }), n.each(i, function (n, t) { e.control("cncb_position").container.find('input[value="' + t + '"]').prop("disabled", !0) }), !o) { var c = e.control("cncb_position").container.find('input[value="top"]'); c.prop("checked", !0), c.trigger("input") } } function a(t, i, o) { if (n.each(t, function (n, t) { e.control("cncb_position").container.find('input[value="' + t + '"]').prop("disabled", !0) }), n.each(i, function (n, t) { e.control("cncb_position").container.find('input[value="' + t + '"]').prop("disabled", !1) }), !o) { var c = e.control("cncb_position").container.find('input[value="top-left"]'); c.prop("checked", !0), c.trigger("input") } } function l(n, e) { t(n, function (n) { var i = t.control(e); i.container[0].style.marginTop = "-12px", n.get() || (i.container[0].style.display = "none"), n.bind(function (n) { n ? i.container[0].style.display = "block" : i.container[0].style.display = "none" }) }) } function r(n, e, i) { t(n, function (n) { var o = t.control(e).container[0], c = t.control(i).container.find("button"); o.style.marginTop = "-12px", n.get() || (o.style.display = "none"), c.prop("disabled", n.get()), n.bind(function (n) { n ? o.style.display = "block" : o.style.display = "none", c.prop("disabled", n) }) }) } jQuery("body").hasClass("wp-customizer") && t.panel.each(function (n) { n.expanded.bind(function (e) { e && "cncb_settings" === n.id ? (jQuery("iframe").contents().find("#cookiebanner-root").show(), jQuery("#customize-controls").addClass("cncb-open")) : (jQuery("iframe").contents().find("#cookiebanner-root").hide(), jQuery("#customize-controls").removeClass("cncb-open")) }) }), t("cncb_widget_type", function (n) { var e = t.control("cncb_vertical_btn").container.find("input"); switch (n.get()) { case "line": c(i, o, !0), e.prop("disabled", 1); break; case "block": a(i, o, !0), e.prop("disabled", 0) }n.bind(function (n) { switch (n) { case "line": c(i, o, !1), e.prop("disabled", 1); break; case "block": a(i, o, !1), e.prop("disabled", 0) } }) }), t("cncb_type", function (n) { var e = t.control("cncb_allow_text"), i = t.control("cncb_decline_text"), o = t.control("cncb_dismiss_text"); switch (n.get()) { case "confirm": e.container[0].style.display = "block", i.container[0].style.display = "block", o.container[0].style.display = "none"; break; case "alert": e.container[0].style.display = "none", i.container[0].style.display = "none", o.container[0].style.display = "block" }n.bind(function (n) { switch (n) { case "confirm": e.container[0].style.display = "block", i.container[0].style.display = "block", o.container[0].style.display = "none"; break; case "alert": e.container[0].style.display = "none", i.container[0].style.display = "none", o.container[0].style.display = "block" } }) }); t("cncb_widget_link_show", function (n) { var e = t.control("cncb_widget_link_text"), i = t.control("cncb_widget_link_href"); n.get() || (e.container[0].style.display = "none", i.container[0].style.display = "none"), n.bind(function (n) { n ? (e.container[0].style.display = "block", i.container[0].style.display = "block") : (e.container[0].style.display = "none", i.container[0].style.display = "none") }) }), l("cncb_shadow", "cncb_shadow_style"), l("cncb_ab_shadow", "cncb_ab_shadow_style"), l("cncb_db_shadow", "cncb_db_shadow_style"), r("cncb_ab_gradient", "cncb_ab_gradient_style", "cncb_ab_bg_color"), r("cncb_ab_hover_gradient", "cncb_ab_hover_gradient_style", "cncb_ab_hover_bg_color"), r("cncb_db_gradient", "cncb_db_gradient_style", "cncb_db_bg_color"), r("cncb_db_hover_gradient", "cncb_db_hover_gradient_style", "cncb_db_hover_bg_color") }) }(jQuery, wp.customize);