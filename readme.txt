=== Cookie Notice & Consent Banner for GDPR & CCPA Compliance ===
Author URI: https://gdprdigest.com
Plugin URI: https://gdprdigest.com
Author: GRPR
Contributors: artemari, kaganvmz
Tags: Cookie, GDPR, CCPA, Compliance, Cookie Consent, Cookie Notice, <PERSON>ie banner, Privacy, Cookie Compliance, Law, Compliant
Requires at least: 5.0
Tested up to: 6.8
Stable tag: trunk
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

== Description ==

Install a Cookie Notice or Consent Banner as Required by Privacy Laws (GDPR & CCPA).
Easily Customizable to Fit Your Design.

== Features ==

* Fit banner to website design by using Wordpress customizer
* Block unnecessary cookies like required by most privacy laws
* Remember user consent for future visits
* Many features to come stay tuned!

The plugin is useful in preparing your site for data protection and privacy regulations:

* GDPR: The General Data Protection Regulation (European Union) DSGVO, RGPD, ОРЗД, ONOOÚ, Persondataforordningen, RGPD, AVG, Rodo, ePrivacy Directive, ePrivacy Regulation
* CCPA: The California Consumer Privacy Act (California, United States)
* PIPEDA: The Personal Information Protection and Electronic Documents Act (Canada)
* DPA: Data Protection Act (2018) (United Kingdom)
* PECR: Privacy and Electronic Communications Regulations (United Kingdom)
* AAP: Australia’s Privacy Principles (Australia)
* LGPD: The Brazilian General Data Protection Law (Brazil)
* PDPA: Personal Data Protection Act (Argentina, Malaysia)
* APPI: Act on the Protection of Personal Information (Japan)
* PDPL: Personal Data Protection Law (Bahrain)
* POPIA: Protection of Personal Information Act (South Africa)
* Law 25: Act Respecting the Protection of Personal Information (Canada Quebec)
* FADP: Federal Act on Data Protection (Switzerland)
* PIPL: Personal Information Protection Law (China)
* 152-FZ: Federal Law on Personal Data (Russia)
* DPDPA: Digital Personal Data Protection Act (India)
* LFPDPPP: Federal Law on Protection of Personal Data (Mexico)	

== Changelog ==
= 1.7.10 - 23. APRIL 2025 =
* Fix: Minor changes

= 1.7.9 - 3. APRIL 2025 =
* Fix: Minor changes

= 1.7.8 - 4. MAR 2025 =
* Fix: Undefined array key 2 in file when user role is author or editor

= 1.7.7 - 10. FEB 2025 =
* Fix: Issues with Dimensions & Space and Wizard sections 
* Fix: Go to the next step link
* Fix: Banner appears in customizer Preview mode

= 1.7.6 - 04. APRIL 2023 =
* Fix: Preview customizer issue

= 1.7.5 - 26. NOV 2022 =
* Fix: Issues with Tabs in the different plugins when you use the Cookie Notice & Consent Banner plugin

= 1.7.4 - 26. MARCH 2022 =
* Fix: Reported issues

= 1.7.3 - 23. DEC 2021 =
* Fix: Wordpress 5.9 compatibility tested

= 1.7.2 - 04. AUG 2021 =
* Fix: Security bugfix
* Fix: Minor changes

= 1.7.1 - 15. JULY 2021 =
* Fix: Wordpress 5.8 compatibility tested
* Fix: Minor changes

= 1.7.0 - 23. JUNE 2021 =
* Fix: Wordpress 5.7.2 compatibility tested
* Fix: Minor changes

= 1.6.9 - 07. MARCH 2021 =
* Fix: Wordpress 5.7 compatibility tested

= 1.6.8 - 25. NOVEMBER 2020 =
* Add: Admin notice on admin dashboard
* Fix: Banner display logic in customizer
* Fix: Styles on settings page
* Fix: Minor changes

= 1.6.7 - 10. NOVEMBER 2020 =
* Add: The deactivation popup appears once
* Add: Splitting a field for nonfunctional JS code into two separate fields for head and body
* Fix: Renamed plugin name in admin menu
* Fix: Minor changes

= 1.6.6 - 23. SEPTEMBER 2020 =
* Fix: Compatibility with page builders
* Fix: Minor changes

= 1.6.5 - 04. SEPTEMBER 2020 =
* Add: New control option "Reload page" (Reload a page after visitor clicks on the "allow" button)
* Fix: Admin settings styles
* Fix: Script blocking output
* Fix: Minor changes

= 1.6.4 - 07. JULY 2020 =
* Fix: Minor changes

= 1.6.3 - 07. JULY 2020 =
* Fix: Tested with WPML compatibility

= 1.6.2 - 06. JULY 2020 =
* Fix: Fixed language textdomain

= 1.6.1 - 04. JULY 2020 =
* Add: New Accept button text
* Fix: Show banner control changed to radio button
* Fix: Wordpress 5.5 compatibility tested
* Fix: Box-shadow error for accept/decline buttons
* Fix: Minor changes

= 1.5.1 - 29. JULY 2020 =
* Add: new button options

= 1.4.1 - 02. JUNE 2020 =
* Fix: Minor fixes

= 1.4.0 - 16. MAY 2020 =
* Add: "on scroll | on time | on click anywhere" controls on cookie plugin admin page
* Update: changed input text to textarea in plugin deactivation box
* Fix: Minor fixes

= 1.3.0 - 06. MAY 2020 =
* Add: New option in cookie consent plugin deactivation window
* Update: Changed default border radius in “block” cookie popup
* Fix: Minor fixes

= 1.2.0 - 30. APRIL 2020 =
* Add: Сookie notice deactivation window
* Fix: Other minor fixes

= 1.1.0 - 22. APRIL 2020 =
* Feature: Implemented border control in Cookie Popup
* Update: Changed Customizer button
* Update: Changed icon in Wordpress Plugin Menu
* Fix: Max-width bug in Cookie Script
* Fix: Other minor fixes

= 1.0.0 =
* List versions from most recent at top to oldest at bottom.
