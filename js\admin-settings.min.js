jQuery("body").hasClass("wp-customizer") && wp.customize.bind("ready", function () { wp.customize.panel.each(function (n) { n.expanded.bind(function (t) { t && "cncb_settings" === n.id ? (jQuery("iframe").contents().find("#cookiebanner-root").show(), jQuery("#customize-controls").addClass("cncb-open")) : (jQuery("iframe").contents().find("#cookiebanner-root").hide(), jQuery("#customize-controls").removeClass("cncb-open")) }) }) }), jQuery(document).ready(function (n) { "use strict"; n(".cncb_parent_field").on("click", function () { n(this).parents("fieldset").find(".cncb_sub_field_wrapper").toggle("show") }), n(".cncb-notice .cncb-notice-dismiss").on("click", function (t) { t.preventDefault(), n.ajax({ type: "POST", url: "admin-ajax.php", data: { action: "cncb_disable_notice" }, success: function (t) { n(".cncb-notice").hide() } }) }), n(".cncb-notice .button").on("click", function (t) { t.preventDefault(); var e = n(this).attr("href"); n.ajax({ type: "POST", url: "admin-ajax.php", data: { action: "cncb_disable_notice" }, success: function (t) { n(".cncb-notice").hide(), window.location.href = e } }) }), n(".cncb-nav-tab-js").on("click", function (t) { t.preventDefault(); var e = n(this).attr("href"); n(".cncb-nav-tab-js").removeClass("nav-tab-active"), n(".content-tab").hide(), n(this).addClass("nav-tab-active"), n(e).show() }), n(".request-feature").on("submit", function (t) { t.preventDefault(); var e = n(this), c = e.find("#feature").val(); n.ajax({ type: "POST", url: "admin-ajax.php", data: { action: "cncb_feature_feedback", feature: c }, success: function (n) { e.find(".button").text("Thanks!") } }) }) });