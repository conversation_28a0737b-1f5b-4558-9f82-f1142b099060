!function(i){"use strict";var t={initBtnText:"Skip & Deactivate",submitBtnText:"Send Feedback & Deactivate",cancelBtnText:"Cancel",progressBtnText:"Sending Feedback...",cacheElements:function(){this.cache={$deactivateLink:i("#the-list").find('[data-slug="cookie-notice-and-consent-banner"] span.deactivate a'),$dialogRadio:i(".cncb-deactivate-feedback-dialog-input"),$dialogInput:i(".cncb-feedback-text"),$dialogForm:i("#cncb-deactivate-feedback-dialog-form"),$dialogSubmitButton:i(".cncb-dialog-submit")}},setCookie:function(t,e,i){var a,n="";i&&((a=new Date).setTime(a.getTime()+24*i*60*60*1e3),n="; expires="+a.toUTCString()),document.cookie=t+"="+(e||"")+n+"; path=/"},getCookie:function(t){for(var e=t+"=",i=document.cookie.split(";"),a=0;a<i.length;a++){for(var n=i[a];" "==n.charAt(0);)n=n.substring(1,n.length);if(0==n.indexOf(e))return n.substring(e.length,n.length)}return null},bindEvents:function(){var e=this;e.cache.$deactivateLink.on("click",function(t){e.getCookie("cncb_show_deactivate_popup")||(t.preventDefault(),e.clearModalState(),e.getModal().dialog("open"),e.setCookie("cncb_show_deactivate_popup","true"))}),e.cache.$dialogRadio.on("change",function(){i(".cncb-dialog-submit").find(".ui-button-text").text(e.submitBtnText)})},deactivate:function(){location.href=this.cache.$deactivateLink.attr("href")},initModal:function(){var e=this;e.getModal=function(){return i("#cncb-deactivate-feedback-dialog-wrapper").dialog({dialogClass:"no-close",autoOpen:!1,height:480,width:590,modal:!0,resizable:!1,buttons:[{id:"cncb-dialog-submit",class:"cncb-dialog-submit",text:e.initBtnText,click:function(){var t;i(".cncb-deactivate-feedback-dialog-input:checked").length?(i(".cncb-dialog-submit").find(".ui-button-text").text(e.progressBtnText),t=e.cache.$dialogForm.serialize(),i.post(ajaxurl,t,e.deactivate.bind(e))):e.deactivate()}},{id:"cncb-dialog-skip",class:"cncb-dialog-skip",text:e.cancelBtnText,click:function(){e.getModal().dialog("close")}}]})}},clearModalState:function(){this.cache.$dialogRadio.removeAttr("checked"),this.cache.$dialogInput.val("")},init:function(){this.initModal(),this.cacheElements(),this.bindEvents()}};i(function(){t.init()})}(jQuery);