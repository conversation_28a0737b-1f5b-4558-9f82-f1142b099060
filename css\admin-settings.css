.toplevel_page_cncb_options .wrap h1 {
    margin-bottom: 10px;
}
.toplevel_page_cncb_options .wrap .container {
    display: flex;
    justify-content: space-between;
}
.cncb-settings-section {
    border-bottom: 1px solid #ccc;
}
.cncb-settings-section.no-line {
    border-bottom: none;
}
.container-left {
    width: calc(100% - 340px);
    padding: 20px;
}
.container-right {
    width: 280px;
}
.popup-box {
    width: 100%;
    background: #fff;
    position: relative;
    box-shadow: 0 0 0 1px rgba(0,0,0,.05);
    padding-bottom: 1.33em;
    margin-bottom: 20px;
}
.popup-box h3 {
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
    padding: .66em 1.33em;
    border-bottom: 1px solid #eee;
}
.popup-box a {
    text-decoration: none;
}
.popup-box p {
    padding-left: 1.33em;
    padding-right: 1.33em;
    margin-bottom: 0;
}
.popup-box .dashicons-star-filled {
    color: #ffb900;
    width: 15px;
    height: 15px;
    font-size: 15px;
    vertical-align: sub;
}
.request-feature {
    padding: 1.33em;
    padding-bottom: 0;
}
.request-feature textarea {
    width: 100%;
}
.description {
    font-size: 13px;
    margin-bottom: 8px;
}
.form-table td fieldset label {
    line-height: 1.4;
}
.form-table td fieldset label {
    margin: .25em 0 .5em !important;
    display: inline-block;
}
.cncb_sub_field_wrapper {
    display: none;
}
.cncb-show {
    display: block;
}
.cncb-form-table .radio-label {
    margin-right: 10px;
}
.cncb-form-table input[type=radio] {
    margin-top: 0;
    margin-right: 0;
}

.cncb-notice {
    position: relative;
}

.cncb-notice .button {
    margin-right: 10px;
    margin-bottom: 10px;
}

.cncb-notice .button.blue-filled {
    color: #fff;
    background-color: #0071a1;
}

.cncb-notice .cncb-notice-dismiss {
    position: absolute;
    top: 0;
    right: 1px;
    border: none;
    margin: 0;
    padding: 9px;
    background: 0 0;
    color: #72777c;
    cursor: pointer;
}

.cncb-notice .cncb-notice-dismiss {
    position: absolute;
    top: 0;
    right: 1px;
    border: none;
    margin: 0;
    padding: 9px;
    background: 0 0;
    color: #72777c;
    cursor: pointer;
}

.cncb-notice .cncb-notice-dismiss:before {
    background: 0 0;
    color: #72777c;
    content: "\f153";
    display: block;
    font: normal 16px/20px dashicons;
    speak: none;
    height: 20px;
    text-align: center;
    width: 20px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.cncb-notice .cncb-notice-dismiss:active:before, .cncb-notice .cncb-notice-dismiss:focus:before, .cncb-notice .cncb-notice-dismiss:hover:before {
    color: #c00;
}

.cncb-notice .cncb-screen-reader-text {
    border: 0;
    clip: rect(1px,1px,1px,1px);
    -webkit-clip-path: inset(50%);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal!important;
}

@media screen and (max-width: 1100px) {
    .container-left {
        width: 100%;
    }
    .container-right {
        display: none;
    }
}